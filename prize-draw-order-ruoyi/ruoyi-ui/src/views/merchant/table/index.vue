<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="桌台号" prop="tableNumber">
        <el-input v-model="queryParams.tableNumber" placeholder="请输入桌台号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="桌台名称" prop="tableName">
        <el-input v-model="queryParams.tableName" placeholder="请输入桌台名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="桌台状态" clearable>
          <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['merchant:table:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['merchant:table:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['merchant:table:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-qrcode" size="mini" :disabled="multiple"
          @click="handleBatchGenerateQrCode">批量生成二维码</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="桌台ID" align="center" prop="tableId" />
      <el-table-column label="桌台号" align="center" prop="tableNumber" />
      <el-table-column label="桌台名称" align="center" prop="tableName" :show-overflow-tooltip="true" />
      <el-table-column label="美团链接" align="center" prop="meituanLink" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-link v-if="scope.row.meituanLink" :href="scope.row.meituanLink" target="_blank" type="primary">
            查看链接
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="二维码" align="center" prop="qrCode" width="100">
        <template slot-scope="scope">
          <el-image v-if="scope.row.qrCode" style="width: 60px; height: 60px" :src="scope.row.qrCode"
            :preview-src-list="[scope.row.qrCode]" fit="cover" />
          <el-button v-else size="mini" type="text" @click="generateQrCode(scope.row)">生成</el-button>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
            @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['merchant:table:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-qrcode" @click="generateQrCode(scope.row)">生成二维码</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['merchant:table:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改桌台对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="桌台号" prop="tableNumber">
              <el-input v-model="form.tableNumber" placeholder="请输入桌台号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="桌台名称" prop="tableName">
              <el-input v-model="form.tableName" placeholder="请输入桌台名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="美团链接" prop="meituanLink">
              <el-input v-model="form.meituanLink" placeholder="请输入美团点餐小程序链接" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                  dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 二维码预览对话框 -->
    <el-dialog title="桌台二维码" :visible.sync="qrCodeVisible" width="400px" append-to-body>
      <div class="qrcode-container">
        <div class="qrcode-info">
          <p><strong>桌台：</strong>{{ currentTable.tableName || currentTable.tableNumber }}</p>
          <p><strong>商家：</strong>{{ merchantName }}</p>
        </div>
        <div class="qrcode-image">
          <el-image v-if="currentQrCode" :src="getQrCodeUrl(currentQrCode)" fit="contain"
            style="width: 300px; height: 300px;" />
        </div>
        <div class="qrcode-actions">
          <el-button type="primary" @click="downloadQrCode">下载二维码</el-button>
          <el-button @click="printQrCode">打印二维码</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMerchantTable,
  getMerchantTable,
  delMerchantTable,
  addMerchantTable,
  updateMerchantTable,
  changeTableStatus,
  generateTableQrCode,
  batchGenerateQrCode
} from "@/api/system/merchantTable"
import { getMerchantId } from "@/api/login"

export default {
  name: "MerchantTable",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 桌台表格数据
      tableList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 二维码预览
      qrCodeVisible: false,
      currentTable: {},
      currentQrCode: '',
      merchantName: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        merchantId: null,
        tableNumber: null,
        tableName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tableNumber: [
          { required: true, message: "桌台号不能为空", trigger: "blur" }
        ],
        tableName: [
          { required: true, message: "桌台名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.initMerchantId();
  },
  methods: {
    /** 初始化商家ID */
    initMerchantId() {
      getMerchantId().then(response => {
        if (response.data) {
          this.queryParams.merchantId = response.data;
          this.getList();
        } else {
          this.$modal.msgError("无法获取商家信息，请联系管理员");
        }
      }).catch(() => {
        this.$modal.msgError("获取商家信息失败");
      });
    },
    /** 查询桌台列表 */
    getList() {
      this.loading = true;
      listMerchantTable(this.queryParams).then(response => {
        this.tableList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tableId: null,
        merchantId: this.queryParams.merchantId,
        tableNumber: null,
        tableName: null,
        meituanLink: null,
        qrCode: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.tableId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加桌台";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const tableId = row.tableId || this.ids
      getMerchantTable(tableId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改桌台";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.tableId != null) {
            updateMerchantTable(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMerchantTable(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const tableIds = row.tableId || this.ids;
      this.$modal.confirm('是否确认删除桌台编号为"' + tableIds + '"的数据项？').then(function () {
        return delMerchantTable(tableIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 桌台状态修改 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.tableName + '"桌台吗？').then(function () {
        return changeTableStatus(row.tableId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    /** 生成二维码 */
    generateQrCode(row) {
      generateTableQrCode(row.tableId).then(response => {
        if (response.code === 200) {
          this.currentTable = row;
          this.currentQrCode = response.data;
          this.qrCodeVisible = true;
          // 更新表格中的二维码
          row.qrCode = response.data;
          this.$modal.msgSuccess("二维码生成成功");
        }
      }).catch(() => {
        this.$modal.msgError("二维码生成失败");
      });
    },
    /** 批量生成二维码 */
    handleBatchGenerateQrCode() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要生成二维码的桌台");
        return;
      }

      this.$modal.confirm('确认要为选中的桌台批量生成二维码吗？').then(() => {
        return batchGenerateQrCode(this.queryParams.merchantId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量生成成功");
      }).catch(() => { });
    },
    /** 下载二维码 */
    downloadQrCode() {
      if (this.currentQrCode) {
        // 使用ruoyi的文件下载功能
        const fileName = `桌台${this.currentTable.tableNumber}_二维码.png`;
        this.$download.resource(this.currentQrCode);
      }
    },
    /** 打印二维码 */
    printQrCode() {
      if (this.currentQrCode) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
          <html>
            <head>
              <title>打印二维码</title>
              <style>
                body { text-align: center; margin: 20px; }
                .qrcode-print { margin: 20px 0; }
                img { max-width: 300px; }
              </style>
            </head>
            <body>
              <h2>${this.merchantName}</h2>
              <h3>桌台：${this.currentTable.tableName || this.currentTable.tableNumber}</h3>
              <div class="qrcode-print">
                <img src="${this.getQrCodeUrl(this.currentQrCode)}" alt="桌台二维码" />
              </div>
              <p>扫码点餐抽奖</p>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    },
    /** 获取二维码完整URL */
    getQrCodeUrl(qrCodePath) {
      if (!qrCodePath) return '';
      // 如果已经是完整URL，直接返回
      if (qrCodePath.startsWith('http')) {
        return qrCodePath;
      }
      // 使用ruoyi的文件访问路径
      return process.env.VUE_APP_BASE_API + qrCodePath;
    }
  }
};
</script>

<style lang="scss" scoped>
.qrcode-container {
  text-align: center;

  .qrcode-info {
    margin-bottom: 20px;

    p {
      margin: 5px 0;
      font-size: 14px;
    }
  }

  .qrcode-image {
    margin: 20px 0;
  }

  .qrcode-actions {
    margin-top: 20px;

    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
